<!--
 * @Description: 个人中心主页 - 仿千库设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 用户信息头部 + 功能图标网格 + 功能列表
-->
<template>
	<view class="user-center ">
		<!-- 用户信息头部 -->
		<view class="user-header">
			<view class="user-info">
				<view class="avatar-wrapper" @click="goToProfile">
					<u-avatar
						:src="userInfo.avatar"
						size="60"
						default-url="/static/icon_exp_default.png"
					></u-avatar>
				</view>
				<view class="user-details" @click="goToProfile">
					<view class="username">{{
						userInfo.userNick || "小蓝助理"
					}}</view>
					<view class="level-container">
						<view class="vip-badge" @click="handleVip" v-if="userInfo.isVip">VIP</view>
						<view class="model-count">{{ modelCountText }}</view>
					</view>
				</view>
				<view class="check-in" @click="handleCheckIn">
					<text class="check-icon">✓</text>
					<text class="check-text">已签到</text>
				</view>
			</view>

			<!-- VIP卡片 -->
			<view
				class="vip-card"
				:class="{ 'vip-active': userInfo.isVip }"
				@click="handleVip"
			>
				<view class="vip-content">
					<view class="vip-title">{{ vipCardTitle }}</view>
					<view class="vip-desc">{{ vipCardDesc }}</view>
				</view>
				<view class="vip-btn">{{ vipButtonText }}</view>
			</view>

			<!-- 用户统计 -->
			<view class="user-stats mt-40">
				<view class="stat-item" @click="goToTodo">
					<view class="stat-number">{{ todoCount }}</view>
					<view class="stat-label">待办</view>
				</view>
				<view class="stat-item" @click="goToAccounting">
					<view class="stat-number">{{ accountingCount }}</view>
					<view class="stat-label">记账</view>
				</view>
				<view class="stat-item" @click="goToMemory">
					<view class="stat-number">{{ memoryCount }}</view>
					<view class="stat-label">记忆</view>
				</view>
				<view class="stat-item" @click="goToKnowledge">
					<view class="stat-number">{{ knowledgeCount }}</view>
					<view class="stat-label">知识库</view>
				</view>
			</view>
		</view>

		<!-- 功能列表 -->
		<view class="function-list">
			<view class="function-row" @click="goToSafety">
				<view class="row-icon">🔒</view>
				<view class="row-title">安全设置</view>
				<view class="row-arrow">›</view>
			</view>
			<view class="function-row" @click="goToAbout">
				<view class="row-icon">ℹ️</view>
				<view class="row-title">关于我们</view>
				<view class="row-arrow">›</view>
			</view>
			<view class="function-row" @click="goToContact">
				<view class="row-icon">📞</view>
				<view class="row-title">联系我们</view>
				<view class="row-arrow">›</view>
			</view>
			<view class="function-row" @click="goToSettings">
				<view class="row-icon">⚙️</view>
				<view class="row-title">应用设置</view>
				<view class="row-arrow">›</view>
			</view>
		</view>

		<!-- 退出登录按钮 -->
		<view class="logout-section p-footer">
			<view class="logout-btn" @click="handleLogout">
				<text>退出登录</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "UserIndex",
	data() {
		return {
			// 用户信息
			userInfo: {
				id: "",
				userNick: "小蓝助理",
				avatar: "",
				level: 8,
				isVip: true, // VIP状态
				email: "", // 邮箱
				mobile: "", // 手机号
			},
			// 统计数据
			todoCount: 12,
			accountingCount: 156,
			memoryCount: 89,
			knowledgeCount: 23,
			// 模型次数
			modelCount: 10,
		};
	},

	computed: {
		/**
		 * 模型次数显示文本
		 */
		modelCountText() {
			if (this.userInfo.isVip) {
				return "Turbo：不限";
			}
			return `Turbo：${this.modelCount}/50次`;
		},

		/**
		 * VIP卡片标题
		 */
		vipCardTitle() {
			return this.userInfo.isVip ? "您已是VIP会员" : "开启千库VIP";
		},

		/**
		 * VIP卡片描述
		 */
		vipCardDesc() {
			return this.userInfo.isVip
				? "尊享全部高级功能和服务"
				: "六大权限一尽享！品质首好年";
		},

		/**
		 * VIP按钮文本
		 */
		vipButtonText() {
			return this.userInfo.isVip ? "续费" : "开通";
		},
	},

	onLoad() {
		this.loadUserData();
	},

	methods: {
		// 加载用户基本信息
		async apiUserInfo() {
			const _res = await this.$ajax.get("/user/apiUser", {}, false);
			if (_res?.code == 200) {
				this.userInfo = _res?.data || {};
				uni.setStorageSync("assistantUserInfo", this.userInfo);
				this.userAvatar = avatarUrl;
			}
		},
		/**
		 * 加载用户数据
		 */
		async loadUserData() {
			await this.apiUserInfo()
		},

		/**
		 * 跳转到个人资料
		 */
		goToProfile() {
			uni.navigateTo({
				url: "/pages_user/profile/index",
			});
		},

		/**
		 * 处理签到
		 */
		handleCheckIn() {
			uni.navigateTo({
				url: "/pages_user/sign/index",
			});
		},

		/**
		 * 处理VIP
		 */
		handleVip() {
			uni.navigateTo({
				url: "/pages_user/vip/index",
			});
		},
		/**
		 * 跳转到会员中心
		 */
		goToVip() {
			uni.navigateTo({
				url: "/pages_user/vip/vip",
			});
		},

		/**
		 * 跳转到安全设置
		 */
		goToSafety() {
			uni.navigateTo({
				url: "/pages_user/safe/index",
			});
		},

		/**
		 * 跳转到联系我们
		 */
		goToContact() {
			uni.navigateTo({
				url: "/pages_user/contact/index",
			});
		},

		/**
		 * 跳转到待办
		 */
		goToTodo() {
			uni.navigateTo({
				url: "/pages_task/index/index",
			});
		},

		/**
		 * 跳转到记账
		 */
		goToAccounting() {
			uni.navigateTo({
				url: "/pages_accounting/index/index",
			});
		},

		/**
		 * 跳转到记忆
		 */
		goToMemory() {
			uni.navigateTo({
				url: "/pages_memory/index/index",
			});
		},

		/**
		 * 跳转到知识库
		 */
		goToKnowledge() {
			uni.navigateTo({
				url: "/pages_wiki/index/index",
			});
		},

		/**
		 * 跳转到关于我们
		 */
		goToAbout() {
			uni.navigateTo({
				url: "/pages_user/about/index",
			});
		},

		/**
		 * 跳转到设置
		 */
		goToSettings() {
			uni.navigateTo({
				url: "/pages_user/settings/index",
			});
		},

		/**
		 * 处理退出登录
		 */
		handleLogout() {
			uni.showModal({
				title: "确认退出",
				content: "确定要退出登录吗？",
				success: (res) => {
					if (res.confirm) {
						// 清除用户数据
						uni.removeStorageSync("assistantUniToken");
						uni.removeStorageSync("assistantUniUserInfo");

						// 跳转到登录页
						uni.reLaunch({
							url: "/pages_chat/login/index",
						});
					}
				},
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.user-center {
	min-height: 100vh;
	background: #f5f5f5;
}

/* 用户信息头部 */
.user-header {
	background: #fff;
	padding: 50rpx 30rpx 30rpx;
	margin-bottom: 20rpx;
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
}

.avatar-wrapper {
	margin-right: 20rpx;
}

.user-details {
	flex: 1;
}

.username {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.level-container {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.model-count {
	background: #007aff;
	color: #fff;
	font-size: 22rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	display: inline-block;
	max-width: 300rpx;
}

.vip-badge {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 30%, #FF8C00 60%, #DAA520 100%);
	color: #8B4513;
	font-size: 22rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-weight: bold;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
	border: 1rpx solid rgba(255, 215, 0, 0.4);
	box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.check-in {
	display: flex;
	align-items: center;
	background: #e8f4fd;
	color: #007aff;
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	font-size: 26rpx;
}

.check-icon {
	margin-right: 8rpx;
}

/* 用户统计 */
.user-stats {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.stat-item {
	text-align: center;
	flex: 1;
}

.stat-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 26rpx;
	color: #666;
}

/* VIP卡片 */
.vip-card {
	background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.3s ease;
}

.vip-card.vip-active {
	background: linear-gradient(135deg, #faf6f0 0%, #f5f0e8 50%, #f0ead6 100%);
	box-shadow: 0 8rpx 25rpx rgba(218, 165, 32, 0.15);
	border: 1rpx solid rgba(218, 165, 32, 0.2);
}

.vip-content {
	flex: 1;
}

.vip-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.vip-card.vip-active .vip-title {
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 30%, #CD853F 60%, #DAA520 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	color: transparent;
}

.vip-desc {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.vip-card.vip-active .vip-desc {
	background: linear-gradient(135deg, #A0522D 0%, #CD853F 50%, #B8860B 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	color: transparent;
	opacity: 0.85;
}

.vip-btn {
	background: rgba(255, 255, 255, 0.2);
	color: #fff;
	padding: 12rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	min-width: 120rpx;
	text-align: center;
}

.vip-card.vip-active .vip-btn {
	background: linear-gradient(135deg, #DAA520 0%, #F4A460 100%);
	color: #fff;
	border: 2rpx solid rgba(218, 165, 32, 0.3);
	box-shadow: 0 4rpx 12rpx rgba(218, 165, 32, 0.2);
}

/* 功能图标区域 */
.function-grid {
	background: #fff;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
}

.function-item {
	text-align: center;
	flex: 1;
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	margin: 0 auto 16rpx;
}

.message-icon {
	background: #e8f4fd;
}

.skin-icon {
	background: #fff2e8;
}

.vip-icon {
	background: #fff0f0;
}

.cloud-icon {
	background: #f0f9ff;
}

.function-label {
	font-size: 26rpx;
	color: #333;
}

/* 功能列表 */
.function-list {
	background: #fff;
}

.function-row {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.function-row:last-child {
	border-bottom: none;
}

.row-icon {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	margin-right: 20rpx;
}

.row-title {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.row-arrow {
	font-size: 40rpx;
	color: #aaa;
}

/* 退出登录区域 */
.logout-section {
	margin-top: 20rpx;
	padding: 20rpx 30rpx 40rpx;
}

.logout-btn {
	background: #fff;
	color: #666;
	text-align: center;
	padding: 20rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	border: 1rpx solid #e0e0e0;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.logout-btn:active {
	background: #f5f5f5;
	transform: scale(0.98);
}
</style>
