<!--
 * @Description: VIP会员中心 - 简约大气设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02 14:31:49
 * @Features: VIP服务介绍、购买引导、支付历史入口
-->
<template>
	<view class="vip-page">
		<!-- 当前状态展示 -->
		<view class="current-status">
			<view class="status-card" :class="{ 'is-vip': userInfo.isVip }">
				<view class="status-icon">
					<text class="icon">{{ userInfo.isVip ? '👑' : '👤' }}</text>
				</view>
				<view class="status-info">
					<view class="status-title">{{ userInfo.isVip ? 'VIP会员' : '普通用户' }}</view>
					<view class="status-desc" v-if="userInfo.isVip">{{ expireTime }}</view>
					<view class="status-desc" v-else>本月高速模型：{{ userInfo.modelUsageCount }}/{{ userInfo.modelLimit }}次</view>
				</view>
			</view>
		</view>

		<!-- 对比卡片区域 -->
		<view class="comparison-section">
			<view class="section-title">
				<view class="title-icon">⚖️</view>
				<view class="title-text">服务对比</view>
				<view class="title-decoration">
					<text class="deco-text">VS</text>
				</view>
			</view>

			<view class="comparison-cards">
				<!-- 普通用户卡片 -->
				<view class="comparison-card normal-card">
					<view class="card-header">
						<view class="card-icon">👤</view>
						<view class="card-title">普通用户</view>
						<view class="card-price">免费</view>
					</view>

					<view class="card-features">
						<view class="feature-item limited">
							<view class="feature-icon">⚡</view>
							<view class="feature-text">每月50次高速模型</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">🤖</view>
							<view class="feature-text">其余使用普通小模型</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">⏱️</view>
							<view class="feature-text">标准响应速度</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">📚</view>
							<view class="feature-text">普通知识问答</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">🧠</view>
							<view class="feature-text">基础学习能力</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">💾</view>
							<view class="feature-text">标准记忆功能</view>
						</view>
						<view class="feature-item limited">
							<view class="feature-icon">🔒</view>
							<view class="feature-text">知识库仅私有</view>
						</view>
					</view>
				</view>

				<!-- VIP用户卡片 -->
				<view class="comparison-card vip-card" :class="{ 'current-plan': userInfo.isVip }">
					<view class="card-header">
						<view class="card-icon">👑</view>
						<view class="card-title">VIP会员</view>
						<view class="card-price">¥29/月</view>
						<view class="recommended-badge" v-if="!userInfo.isVip">推荐</view>
					</view>

					<view class="card-features">
						<view class="feature-item">
							<view class="feature-icon">⚡</view>
							<view class="feature-text">无限次高速旗舰模型</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🚀</view>
							<view class="feature-text">优先响应处理</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🎯</view>
							<view class="feature-text">所有AI任务高速模型</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🔬</view>
							<view class="feature-text">超高精RAG支持</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🧬</view>
							<view class="feature-text">Self RAG技术加持</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🎓</view>
							<view class="feature-text">Self Rewarding能力</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🧠</view>
							<view class="feature-text">Memory强化学习</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🔍</view>
							<view class="feature-text">COCOM上下文压缩</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">🌐</view>
							<view class="feature-text">知识库对外开放权限</view>
						</view>
						<view class="feature-item">
							<view class="feature-icon">💎</view>
							<view class="feature-text">专属客服支持</view>
						</view>
					</view>

					<view class="card-action">
						<u-button
							v-if="!userInfo.isVip"
							type="primary"
							size="large"
							:custom-style="vipButtonStyle"
							@click="goToWebPurchase"
						>
							立即开通
						</u-button>
						<u-button
							v-else
							type="primary"
							size="large"
							plain
							:custom-style="renewButtonStyle"
							@click="goToWebPurchase"
						>
							续费
						</u-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 购买说明 -->
		<view class="purchase-notice">
			<view class="notice-content">
				<view class="notice-icon">💡</view>
				<view class="notice-text">
					<text class="notice-title">购买说明</text>
					<text class="notice-desc">暂不支持应用内购买，请前往网页端完成购买操作</text>
				</view>
			</view>

			<view class="notice-actions" v-if="!userInfo.isVip">
				<u-button
					type="info"
					size="normal"
					plain
					:custom-style="refreshButtonStyle"
					@click="refreshVipStatus"
				>
					我已购买，刷新状态
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
/**
 * VIP会员中心页面逻辑
 * 功能：VIP状态展示、特权介绍、购买引导
 */
export default {
	name: "VipIndex",
	data() {
		return {
			// 用户信息（模拟数据，实际应从store或API获取）
			userInfo: {
				isVip: false,
				vipExpireTime: null,
				nickname: "小蓝助理",
				modelUsageCount: 25, // 本月已使用次数
				modelLimit: 50 // 普通用户月限制
			},

			// VIP特权列表（用于详细说明）
			privileges: [
				{
					icon: "⚡",
					title: "无限次高速旗舰模型对话",
					desc: "享受最强AI能力，无使用次数限制",
					isNew: false
				},
				{
					icon: "🚀",
					title: "优先响应处理",
					desc: "VIP用户请求优先处理，响应更快",
					isNew: false
				},
				{
					icon: "🔬",
					title: "超高精RAG支持",
					desc: "高精度检索增强生成，提供更准确的知识问答",
					isNew: true
				},
				{
					icon: "🧬",
					title: "Self RAG自我检索",
					desc: "AI自主判断是否需要检索，智能优化回答质量",
					isNew: true
				},
				{
					icon: "🎓",
					title: "Self Rewarding自我学习",
					desc: "AI自我评估和改进，持续提升服务质量",
					isNew: true
				},
				{
					icon: "🧠",
					title: "Memory强化学习",
					desc: "根据用户操作习惯不断适配，提供个性化服务",
					isNew: true
				},
				{
					icon: "🌐",
					title: "知识库对外开放权限",
					desc: "支持知识库分享和协作，扩展知识边界",
					isNew: true
				},
				{
					icon: "💎",
					title: "专属客服支持",
					desc: "享受一对一专属客服，解决使用问题",
					isNew: false
				}
			],

			// 按钮样式
			vipButtonStyle: {
				borderRadius: '12rpx',
				height: '80rpx',
				fontSize: '28rpx',
				fontWeight: '600',
				background: 'linear-gradient(135deg, #1890ff, #40a9ff)'
			},

			renewButtonStyle: {
				borderRadius: '12rpx',
				height: '80rpx',
				fontSize: '28rpx',
				fontWeight: '600',
				borderColor: '#1890ff',
				color: '#1890ff'
			},

			refreshButtonStyle: {
				borderRadius: '8rpx',
				height: '64rpx',
				fontSize: '24rpx',
				borderColor: '#1890ff',
				color: '#1890ff'
			}
		};
	},

	computed: {
		// VIP状态标题
		statusTitle() {
			return this.userInfo.isVip ? "VIP会员" : "普通用户";
		},

		// VIP状态描述
		statusDesc() {
			if (this.userInfo.isVip) {
				return "尊享全部特权服务";
			} else {
				const used = this.userInfo.modelUsageCount;
				const limit = this.userInfo.modelLimit;
				return `本月高速模型使用：${used}/${limit}次`;
			}
		},

		// VIP到期时间格式化
		expireTime() {
			if (!this.userInfo.vipExpireTime) return "永久有效";

			const date = new Date(this.userInfo.vipExpireTime);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');

			return `${year}-${month}-${day}`;
		}
	},

	onLoad() {
		// 页面加载时获取用户VIP状态
		this.loadUserVipStatus();
	},

	onShow() {
		// 页面显示时刷新VIP状态（从其他页面返回时）
		this.loadUserVipStatus();
	},

	onNavigationBarButtonTap(e) {
		// 处理导航栏右侧按钮点击
		if (e.index === 0) {
			// 跳转到支付历史页面
			uni.navigateTo({
				url: '/pages_user/vip/pay'
			});
		}
	},

	methods: {

		/**
		 * 跳转到网页购买页面
		 */
		goToWebPurchase() {
			// 显示确认弹窗
			this.$confirm1({
				title: '购买提示',
				content: '即将跳转到网页端完成购买，是否继续？',
				confirmText: '继续',
				cancelText: '取消'
			}).then(() => {
				// 跳转到外部网页
				// #ifdef H5
				window.open('http://www.baidu.com', '_blank');
				// #endif

				// #ifdef APP-PLUS
				plus.runtime.openURL('http://www.baidu.com');
				// #endif

				// #ifdef MP
				uni.showModal({
					title: '提示',
					content: '请复制链接到浏览器打开：http://www.baidu.com',
					showCancel: false
				});
				// #endif
			}).catch(() => {
				// 用户取消操作
			});
		},

		/**
		 * 刷新VIP状态
		 */
		refreshVipStatus() {
			uni.showLoading({
				title: '刷新中...'
			});

			// 模拟API请求
			setTimeout(() => {
				this.loadUserVipStatus();
				uni.hideLoading();

				uni.showToast({
					title: '状态已刷新',
					icon: 'success'
				});
			}, 1500);
		},

		/**
		 * 加载用户VIP状态
		 * 实际项目中应该调用API获取真实数据
		 */
		loadUserVipStatus() {
			// 模拟从API获取用户VIP状态
			// 这里应该替换为真实的API调用

			// 示例：从本地存储获取用户信息
			try {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = {
						...this.userInfo,
						...userInfo
					};
				}
			} catch (e) {
				console.error('获取用户信息失败:', e);
			}

			// 模拟VIP状态（实际应从服务器获取）
			// this.userInfo.isVip = false;
			// this.userInfo.vipExpireTime = null;
		}
	}
};
</script>

<style lang="scss" scoped>
// Ant Design 主色调
$primary-color: #1890ff;
$primary-light: #e6f7ff;
$primary-dark: #096dd9;

// 基础色彩
$white: #ffffff;
$text-primary: #262626;
$text-secondary: #8c8c8c;
$text-light: #bfbfbf;
$border-color: #f0f0f0;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;

// 圆角
$border-radius-sm: 4rpx;
$border-radius-md: 8rpx;
$border-radius-lg: 12rpx;
$border-radius-xl: 16rpx;

// 字体大小
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;

.vip-page {
	background-color: $white;
	min-height: 100vh;
	padding: $spacing-lg;
}

// 当前状态展示
.current-status {
	margin-bottom: $spacing-xl;

	.status-card {
		background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
		border-radius: $border-radius-lg;
		padding: $spacing-lg;
		border: 2rpx solid $border-color;
		display: flex;
		align-items: center;
		transition: all 0.3s ease;

		&.is-vip {
			background: linear-gradient(135deg, $primary-light 0%, #ffffff 100%);
			border-color: $primary-color;
			box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
		}

		.status-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			background-color: $white;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: $spacing-lg;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

			.icon {
				font-size: 40rpx;
			}
		}

		.status-info {
			flex: 1;

			.status-title {
				font-size: $font-size-lg;
				font-weight: 600;
				color: $text-primary;
				margin-bottom: $spacing-xs;
			}

			.status-desc {
				font-size: $font-size-sm;
				color: $text-secondary;
			}
		}
	}
}

// 对比区域
.comparison-section {
	margin-bottom: $spacing-xl;

	.section-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: $spacing-lg;
		position: relative;

		.title-icon {
			font-size: 32rpx;
			margin-right: $spacing-sm;
		}

		.title-text {
			font-size: $font-size-lg;
			font-weight: 600;
			color: $text-primary;
		}

		.title-decoration {
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);

			.deco-text {
				font-size: $font-size-sm;
				font-weight: 700;
				color: $primary-color;
				background: linear-gradient(135deg, $primary-light, $primary-color);
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				border: 2rpx solid $primary-color;
			}
		}
	}
}

.comparison-cards {
	display: flex;
	gap: $spacing-md;

	.comparison-card {
		flex: 1;
		background-color: $white;
		border-radius: $border-radius-lg;
		border: 2rpx solid $border-color;
		overflow: hidden;
		transition: all 0.3s ease;
		min-height: 680rpx;
		display: flex;
		flex-direction: column;

		&.normal-card {
			.card-header {
				background-color: #fafafa;
				border-bottom: 1rpx solid $border-color;
				color: $text-primary;
			}
		}

		&.vip-card {
			position: relative;

			.card-header {
				background: linear-gradient(135deg, $primary-color, $primary-dark);
				color: $white;
			}

			&.current-plan {
				border-color: $primary-color;
				box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
			}
		}

		.card-header {
			padding: $spacing-lg;
			text-align: center;
			position: relative;

			.card-icon {
				font-size: 36rpx;
				margin-bottom: $spacing-sm;
				display: block;
			}

			.card-title {
				font-size: $font-size-md;
				font-weight: 600;
				margin-bottom: $spacing-xs;
			}

			.card-price {
				font-size: $font-size-lg;
				font-weight: 700;
			}

			.recommended-badge {
				position: absolute;
				top: -2rpx;
				right: -2rpx;
				background-color: $error-color;
				color: $white;
				font-size: 20rpx;
				padding: 8rpx 16rpx;
				border-radius: 0 $border-radius-lg 0 $border-radius-lg;
				font-weight: 600;
			}
		}

		.card-features {
			padding: $spacing-lg;
			flex: 1;

			.feature-item {
				display: flex;
				align-items: flex-start;
				margin-bottom: $spacing-md;

				&:last-child {
					margin-bottom: 0;
				}

				&.limited {
					opacity: 0.6;
				}

				.feature-icon {
					font-size: 24rpx;
					margin-right: $spacing-sm;
					width: 32rpx;
					margin-top: 4rpx;
					flex-shrink: 0;
				}

				.feature-text {
					font-size: $font-size-sm;
					color: $text-primary;
					line-height: 1.5;
					flex: 1;
				}
			}
		}

		.card-action {
			padding: $spacing-lg;
			padding-top: 0;
			margin-top: auto;
		}
	}
}

// 购买说明
.purchase-notice {
	background-color: #f0f9ff;
	border-radius: $border-radius-lg;
	padding: $spacing-lg;
	border: 1rpx solid #bae6fd;

	.notice-content {
		display: flex;
		align-items: flex-start;
		margin-bottom: $spacing-md;

		.notice-icon {
			font-size: 32rpx;
			margin-right: $spacing-md;
			margin-top: 4rpx;
		}

		.notice-text {
			flex: 1;

			.notice-title {
				font-size: $font-size-md;
				font-weight: 600;
				color: $primary-color;
				margin-bottom: $spacing-xs;
				display: block;
			}

			.notice-desc {
				font-size: $font-size-sm;
				color: $text-secondary;
				display: block;
				line-height: 1.5;
			}
		}
	}

	.notice-actions {
		text-align: center;
	}
}

// 响应式适配
@media screen and (max-width: 600rpx) {
	.comparison-section {
		.section-title {
			flex-direction: column;

			.title-decoration {
				position: static;
				transform: none;
				margin-top: $spacing-sm;
			}
		}
	}

	.comparison-cards {
		flex-direction: column;

		.comparison-card {
			margin-bottom: $spacing-md;
			min-height: auto;

			&:last-child {
				margin-bottom: 0;
			}

			.card-header {
				padding: $spacing-md;

				.card-icon {
					font-size: 32rpx;
				}

				.card-title {
					font-size: $font-size-sm;
				}

				.card-price {
					font-size: $font-size-md;
				}
			}

			.card-features {
				padding: $spacing-md;

				.feature-item {
					margin-bottom: $spacing-sm;

					.feature-icon {
						font-size: 20rpx;
						width: 28rpx;
					}

					.feature-text {
						font-size: 22rpx;
					}
				}
			}
		}
	}

	.current-status {
		.status-card {
			padding: $spacing-md;

			.status-icon {
				width: 60rpx;
				height: 60rpx;

				.icon {
					font-size: 32rpx;
				}
			}

			.status-info {
				.status-title {
					font-size: $font-size-md;
				}

				.status-desc {
					font-size: 22rpx;
				}
			}
		}
	}
}
</style>
